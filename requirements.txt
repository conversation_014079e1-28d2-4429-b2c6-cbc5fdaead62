# Core Python packages
ipython
jupyter
# LangGraph with visualization support
langgraph[visualization]
# Optional fallback if <PERSON><PERSON> fails
pygraphviz
graphviz

# LangChain (chat model support)
langchain[openai]

# LangGraph for graph-based workflows
langgraph
langsmith

# If you're using OpenAI chat models (like gpt-4.1), you need the OpenAI client
openai

# Typing extensions for backward compatibility (TypedDict, Annotated, etc.)
typing_extensions

langchain-tavily