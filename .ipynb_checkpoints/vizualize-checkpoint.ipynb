{"cells": [{"cell_type": "code", "execution_count": null, "id": "faa8c3a7-c515-4643-98e5-168250810d96", "metadata": {}, "outputs": [], "source": ["from IPython.display import Image, display\n", "from chatbot import graph\n", "\n", "try:\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass\n", "\n", "# Use Jupyter notebook for visualization"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}