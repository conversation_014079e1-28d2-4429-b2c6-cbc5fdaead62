from dotenv import load_dotenv
from langchain.chat_models import init_chat_model

# Load environment variables from .env file
load_dotenv()

from typing import Annotated

from typing_extensions import TypedDict

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

from langgraph.prebuilt import ToolN<PERSON>, tools_condition

from langchain_tavily import TavilySearch
from MemorySaver import memory

class State(TypedDict):
    # Messages have the type "list". The `add_messages` function
    # in the annotation defines how this state key should be updated
    # (in this case, it appends messages to the list, rather than overwriting them)
    messages: Annotated[list, add_messages]

graph_builder = StateGraph(State)

# API keys are now loaded from .env file via load_dotenv()

llm = init_chat_model("openai:gpt-4.1")

tool = TavilySearch(max_results=2)
tools = [tool]

# Modification: tell the LLM which tools it can call
# highlight-next-line
llm_with_tools = llm.bind_tools(tools)

def chatbot(state: State):
    return {"messages": [llm_with_tools.invoke(state["messages"])]}

# The first argument is the unique node name
# The second argument is the function or object that will be called whenever
# the node is used.
graph_builder.add_node("chatbot", chatbot)

tool_node = ToolNode(tools=[tool])
graph_builder.add_node("tools", tool_node)
graph_builder.add_edge(START, "chatbot")
graph_builder.add_conditional_edges(
    "chatbot",
    tools_condition,
)
graph_builder.add_edge("chatbot", END)
graph = graph_builder.compile(checkpointer=memory)

def stream_graph_updates(user_input: str, thread_id: str = "1"):
    # Create config with thread_id for memory persistence
    config = {"configurable": {"thread_id": thread_id}}

    # Stream with config as second argument
    for event in graph.stream(
        {"messages": [{"role": "user", "content": user_input}]},
        config,
        stream_mode="values"
    ):
        event["messages"][-1].pretty_print()

# Main loop with threading support
def main():
    print("🤖 LangGraph Chatbot with Memory!")
    print("💡 Each conversation has its own thread. Type 'new' to start a new thread.")
    print("🚪 Type 'quit', 'exit', or 'q' to exit.\n")

    current_thread = "1"  # Default thread

    while True:
        try:
            user_input = input(f"User (Thread {current_thread}): ")

            if user_input.lower() in ["quit", "exit", "q"]:
                print("Goodbye! 👋")
                break
            elif user_input.lower() == "new":
                # Start a new thread
                import time
                current_thread = str(int(time.time()))  # Use timestamp as thread ID
                print(f"🆕 Started new conversation thread: {current_thread}")
                continue
            elif user_input.lower() == "threads":
                print(f"📋 Current thread: {current_thread}")
                continue

            stream_graph_updates(user_input, current_thread)

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            # Fallback for testing
            print("🔄 Testing with fallback question...")
            stream_graph_updates("What do you know about LangGraph?", current_thread)
            break

if __name__ == "__main__":
    main()

# Main loop - this is the entry point!
while True:
    try:
        user_input = input("User: ")
        if user_input.lower() in ["quit", "exit", "q"]:
            print("Goodbye!")
            break
        stream_graph_updates(user_input)
    except KeyboardInterrupt:
        print("\nGoodbye!")
        break
    except Exception as e:
        print(f"Error: {e}")
        # fallback if input() is not available
        user_input = "What do you know about LangGraph?"
        print("User: " + user_input)
        stream_graph_updates(user_input)
        break